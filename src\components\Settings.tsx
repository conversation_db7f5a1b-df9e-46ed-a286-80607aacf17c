import React, { useState, useEffect } from 'react';
import { X, Eye, EyeOff, Check, AlertCircle, RefreshCw, Settings as SettingsIcon } from 'lucide-react';
import { APISettings, AIProvider, GeminiModel } from '../types/mandala';
import { LoadingSpinner } from './LoadingSpinner';

interface SettingsProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (settings: APISettings) => void;
  currentSettings: APISettings | null;
  onValidateKey: (provider: AIProvider, apiKey: string) => Promise<{ isValid: boolean; error?: string; models?: GeminiModel[] }>;
  onGetModels: (provider: AIProvider) => Promise<GeminiModel[]>;
}

export const Settings: React.FC<SettingsProps> = ({
  isOpen,
  onClose,
  onSave,
  currentSettings,
  onValidateKey,
  onGetModels,
}) => {
  const [settings, setSettings] = useState<APISettings>({
    provider: 'gemini',
    gemini: {
      apiKey: '',
      model: 'gemini-1.5-flash',
      availableModels: [],
    },
    maxTokens: 150,
    temperature: 0.7,
  });

  const [showApiKey, setShowApiKey] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    error?: string;
  } | null>(null);
  const [isLoadingModels, setIsLoadingModels] = useState(false);

  // 初始化设置
  useEffect(() => {
    if (currentSettings) {
      setSettings(currentSettings);
    }
  }, [currentSettings]);

  // 处理提供商切换
  const handleProviderChange = (provider: AIProvider) => {
    setSettings(prev => ({
      ...prev,
      provider,
    }));
    setValidationResult(null);
  };

  // 处理 API 密钥变化
  const handleApiKeyChange = (apiKey: string) => {
    if (settings.provider === 'gemini' && settings.gemini) {
      setSettings(prev => ({
        ...prev,
        gemini: {
          ...prev.gemini!,
          apiKey,
        },
      }));
    }
    setValidationResult(null);
  };

  // 验证 API 密钥
  const handleValidateKey = async () => {
    const apiKey = settings.provider === 'gemini' ? settings.gemini?.apiKey : '';
    if (!apiKey) {
      setValidationResult({ isValid: false, error: 'API 密钥不能为空' });
      return;
    }

    setIsValidating(true);
    try {
      const result = await onValidateKey(settings.provider, apiKey);
      setValidationResult(result);
      
      if (result.isValid && result.models && settings.provider === 'gemini' && settings.gemini) {
        setSettings(prev => ({
          ...prev,
          gemini: {
            ...prev.gemini!,
            availableModels: result.models!,
            lastModelsFetch: new Date(),
          },
        }));
      }
    } catch (error) {
      setValidationResult({
        isValid: false,
        error: error instanceof Error ? error.message : '验证失败',
      });
    } finally {
      setIsValidating(false);
    }
  };

  // 刷新模型列表
  const handleRefreshModels = async () => {
    setIsLoadingModels(true);
    try {
      const models = await onGetModels(settings.provider);
      if (settings.provider === 'gemini' && settings.gemini) {
        setSettings(prev => ({
          ...prev,
          gemini: {
            ...prev.gemini!,
            availableModels: models,
            lastModelsFetch: new Date(),
          },
        }));
      }
    } catch (error) {
      console.error('刷新模型列表失败:', error);
    } finally {
      setIsLoadingModels(false);
    }
  };

  // 处理模型选择
  const handleModelChange = (model: string) => {
    if (settings.provider === 'gemini' && settings.gemini) {
      setSettings(prev => ({
        ...prev,
        gemini: {
          ...prev.gemini!,
          model,
        },
      }));
    }
  };

  // 保存设置
  const handleSave = () => {
    onSave(settings);
    onClose();
  };

  if (!isOpen) return null;

  const currentApiKey = settings.provider === 'gemini' ? settings.gemini?.apiKey || '' : '';
  const availableModels = settings.provider === 'gemini' ? settings.gemini?.availableModels || [] : [];
  const currentModel = settings.provider === 'gemini' ? settings.gemini?.model || '' : '';

  return (
    <div className="dialog-overlay">
      <div className="dialog-content max-w-2xl">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <SettingsIcon size={24} className="text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">API 设置</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* 设置内容 */}
        <div className="p-6 space-y-6">
          {/* AI 提供商选择 */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              AI 提供商
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="provider"
                  value="gemini"
                  checked={settings.provider === 'gemini'}
                  onChange={() => handleProviderChange('gemini')}
                  className="mr-2"
                />
                <span className="text-sm">Google Gemini</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="provider"
                  value="openai"
                  checked={settings.provider === 'openai'}
                  onChange={() => handleProviderChange('openai')}
                  className="mr-2"
                  disabled
                />
                <span className="text-sm text-gray-400">OpenAI (即将支持)</span>
              </label>
            </div>
          </div>

          {/* Gemini API 设置 */}
          {settings.provider === 'gemini' && (
            <div className="space-y-4">
              {/* API 密钥 */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Gemini API 密钥
                </label>
                <div className="relative">
                  <input
                    type={showApiKey ? 'text' : 'password'}
                    value={currentApiKey}
                    onChange={(e) => handleApiKeyChange(e.target.value)}
                    placeholder="输入您的 Gemini API 密钥"
                    className="w-full px-3 py-2 pr-20 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                    <button
                      type="button"
                      onClick={() => setShowApiKey(!showApiKey)}
                      className="p-1 hover:bg-gray-100 rounded"
                    >
                      {showApiKey ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                    <button
                      type="button"
                      onClick={handleValidateKey}
                      disabled={isValidating || !currentApiKey}
                      className="p-1 hover:bg-gray-100 rounded disabled:opacity-50"
                    >
                      {isValidating ? (
                        <LoadingSpinner size="sm" />
                      ) : (
                        <Check size={16} />
                      )}
                    </button>
                  </div>
                </div>
                
                {/* 验证结果 */}
                {validationResult && (
                  <div className={`flex items-center space-x-2 text-sm ${
                    validationResult.isValid ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {validationResult.isValid ? (
                      <Check size={16} />
                    ) : (
                      <AlertCircle size={16} />
                    )}
                    <span>
                      {validationResult.isValid 
                        ? 'API 密钥验证成功' 
                        : validationResult.error || '验证失败'
                      }
                    </span>
                  </div>
                )}
                
                <p className="text-xs text-gray-500">
                  您可以在 <a 
                    href="https://aistudio.google.com/app/apikey" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    Google AI Studio
                  </a> 获取免费的 API 密钥
                </p>
              </div>

              {/* 模型选择 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="block text-sm font-medium text-gray-700">
                    模型选择
                  </label>
                  <button
                    onClick={handleRefreshModels}
                    disabled={isLoadingModels || !currentApiKey}
                    className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50"
                  >
                    <RefreshCw size={14} className={isLoadingModels ? 'animate-spin' : ''} />
                    <span>刷新</span>
                  </button>
                </div>
                <select
                  value={currentModel}
                  onChange={(e) => handleModelChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={availableModels.length === 0}
                >
                  {availableModels.length === 0 ? (
                    <option value="">请先验证 API 密钥以获取模型列表</option>
                  ) : (
                    availableModels.map((model) => (
                      <option key={model.name} value={model.name}>
                        {model.displayName}
                      </option>
                    ))
                  )}
                </select>
              </div>
            </div>
          )}

          {/* 生成参数 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">生成参数</h3>
            
            {/* 最大 Token 数 */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                最大 Token 数: {settings.maxTokens}
              </label>
              <input
                type="range"
                min="50"
                max="500"
                step="10"
                value={settings.maxTokens}
                onChange={(e) => setSettings(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>50</span>
                <span>500</span>
              </div>
            </div>

            {/* 温度参数 */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                创造性 (Temperature): {settings.temperature}
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={settings.temperature}
                onChange={(e) => setSettings(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>保守 (0)</span>
                <span>创新 (1)</span>
              </div>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="btn-primary"
            disabled={!currentApiKey || (validationResult && !validationResult.isValid)}
          >
            保存设置
          </button>
        </div>
      </div>
    </div>
  );
};
