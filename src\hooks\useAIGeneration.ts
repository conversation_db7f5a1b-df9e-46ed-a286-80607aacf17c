import { useState } from 'react';
import { useMandala } from '../contexts/MandalaContext';
import { aiService } from '../services/aiService';
import { findNodeById, getNodePath, initializeGridChildren } from '../utils/mandala';
import { AIGenerationRequest, MandalaNode, GRID_POSITIONS } from '../types/mandala';

export const useAIGeneration = () => {
  const { state, dispatch } = useMandala();
  const [isGenerating, setIsGenerating] = useState(false);
  
  // 生成单个节点内容
  const generateSingleNode = async (nodeId: string): Promise<boolean> => {
    if (!state.currentChart || isGenerating) return false;
    
    setIsGenerating(true);
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      const targetNode = findNodeById(state.currentChart.rootNode, nodeId);
      if (!targetNode) {
        throw new Error('找不到目标节点');
      }
      
      // 构建上下文路径
      const nodePath = getNodePath(state.currentChart.rootNode, nodeId);
      const context = nodePath.map(node => node.content).filter(content => content.trim() !== '');
      
      // 构建AI请求
      const request: AIGenerationRequest = {
        parentContent: targetNode.parentId ? 
          findNodeById(state.currentChart.rootNode, targetNode.parentId)?.content || '' : '',
        level: targetNode.level,
        position: targetNode.position,
        context,
      };
      
      // 调用AI生成
      const generatedContent = await aiService.generateSingleContent(request);
      
      if (generatedContent) {
        // 更新节点内容
        dispatch({
          type: 'UPDATE_NODE_CONTENT',
          payload: { nodeId, content: generatedContent },
        });
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('AI生成失败:', error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof Error ? error.message : 'AI生成失败' 
      });
      return false;
    } finally {
      setIsGenerating(false);
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };
  
  // 生成整个九宫格的内容
  const generateFullGrid = async (centerNodeId: string): Promise<boolean> => {
    if (!state.currentChart || isGenerating) return false;
    
    setIsGenerating(true);
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      const centerNode = findNodeById(state.currentChart.rootNode, centerNodeId);
      if (!centerNode) {
        throw new Error('找不到中心节点');
      }
      
      // 构建上下文路径
      const nodePath = getNodePath(state.currentChart.rootNode, centerNodeId);
      const context = nodePath.map(node => node.content).filter(content => content.trim() !== '');
      
      // 构建AI请求
      const request: AIGenerationRequest = {
        parentContent: centerNode.content,
        level: centerNode.level + 1,
        position: GRID_POSITIONS.CENTER,
        context,
      };
      
      // 调用AI批量生成
      const generatedContents = await aiService.generateBatchContent(request);
      
      if (generatedContents.length >= 8) {
        // 初始化或获取子节点
        let childNodes = centerNode.children;
        if (childNodes.length === 0) {
          childNodes = initializeGridChildren(centerNode);
        }
        
        // 更新除中心位置外的所有子节点内容
        const updatePromises = childNodes
          .filter(child => child.position !== GRID_POSITIONS.CENTER)
          .map((child, index) => {
            const content = generatedContents[index] || `内容${index + 1}`;
            return new Promise<void>((resolve) => {
              dispatch({
                type: 'UPDATE_NODE_CONTENT',
                payload: { nodeId: child.id, content },
              });
              resolve();
            });
          });
        
        await Promise.all(updatePromises);
        
        // 导航到生成的九宫格
        dispatch({ type: 'NAVIGATE_TO_NODE', payload: centerNodeId });
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('AI批量生成失败:', error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof Error ? error.message : 'AI批量生成失败' 
      });
      return false;
    } finally {
      setIsGenerating(false);
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };
  
  // 重新生成节点内容
  const regenerateNode = async (nodeId: string): Promise<boolean> => {
    return await generateSingleNode(nodeId);
  };
  
  // 智能生成：根据节点状态决定生成策略
  const smartGenerate = async (nodeId: string): Promise<boolean> => {
    if (!state.currentChart) return false;
    
    const targetNode = findNodeById(state.currentChart.rootNode, nodeId);
    if (!targetNode) return false;
    
    // 如果是空节点，生成单个内容
    if (targetNode.content.trim() === '') {
      return await generateSingleNode(nodeId);
    }
    
    // 如果是有内容的节点且可以展开下一层，生成整个九宫格
    if (targetNode.level < (state.currentChart.maxLevel || 4)) {
      return await generateFullGrid(nodeId);
    }
    
    // 否则重新生成当前节点内容
    return await regenerateNode(nodeId);
  };
  
  // 设置API密钥（保持向后兼容）
  const setApiKey = (apiKey: string) => {
    aiService.setApiKey(apiKey);
  };
  
  return {
    isGenerating,
    generateSingleNode,
    generateFullGrid,
    regenerateNode,
    smartGenerate,
    setApiKey,
  };
};
