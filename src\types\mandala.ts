// 曼陀罗节点数据结构
export interface MandalaNode {
  id: string;
  content: string;
  level: number; // 层级：1=主题，2=维度，3=子主题，4=执行步骤
  position: number; // 在九宫格中的位置 (0-8, 4为中心)
  parentId?: string; // 父节点ID
  children: MandalaNode[]; // 子节点数组
  isEditable: boolean; // 是否可编辑
  createdAt: Date;
  updatedAt: Date;
}

// 曼陀罗图数据结构
export interface MandalaChart {
  id: string;
  title: string;
  rootNode: MandalaNode;
  currentViewNodeId: string; // 当前视图的中心节点ID
  maxLevel: number; // 最大层级数
  createdAt: Date;
  updatedAt: Date;
}

// 导航历史记录
export interface NavigationHistory {
  nodeId: string;
  timestamp: Date;
}

// 应用状态
export interface AppState {
  currentChart: MandalaChart | null;
  isEditMode: boolean;
  navigationHistory: NavigationHistory[];
  currentHistoryIndex: number;
  isLoading: boolean;
  error: string | null;
  apiSettings: APISettings | null;
  isSettingsOpen: boolean;
}

// AI提供商类型
export type AIProvider = 'gemini' | 'openai';

// Gemini模型信息
export interface GeminiModel {
  name: string;
  displayName: string;
  description?: string;
  inputTokenLimit?: number;
  outputTokenLimit?: number;
  supportedGenerationMethods?: string[];
  temperature?: number;
  topP?: number;
  topK?: number;
}

// API设置接口
export interface APISettings {
  provider: AIProvider;
  gemini?: {
    apiKey: string;
    model: string;
    availableModels: GeminiModel[];
    lastModelsFetch?: Date;
  };
  openai?: {
    apiKey: string;
    model: string;
    baseURL?: string;
  };
  maxTokens: number;
  temperature: number;
}

// API密钥验证结果
export interface APIKeyValidationResult {
  isValid: boolean;
  error?: string;
  models?: GeminiModel[];
}

// AI生成配置（保持向后兼容）
export interface AIGenerationConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
}

// AI生成请求
export interface AIGenerationRequest {
  parentContent: string;
  level: number;
  position: number;
  context: string[];
}

// AI生成响应
export interface AIGenerationResponse {
  content: string;
  suggestions: string[];
}

// 九宫格位置映射
export const GRID_POSITIONS = {
  TOP_LEFT: 0,
  TOP_CENTER: 1,
  TOP_RIGHT: 2,
  MIDDLE_LEFT: 3,
  CENTER: 4,
  MIDDLE_RIGHT: 5,
  BOTTOM_LEFT: 6,
  BOTTOM_CENTER: 7,
  BOTTOM_RIGHT: 8,
} as const;

// 层级配置
export const LEVEL_CONFIG = {
  THEME: 1,      // 主题
  DIMENSION: 2,  // 维度
  SUBTOPIC: 3,   // 子主题
  ACTION: 4,     // 执行步骤
} as const;

// 默认九宫格布局
export const DEFAULT_GRID_LAYOUT = [
  GRID_POSITIONS.TOP_LEFT,
  GRID_POSITIONS.TOP_CENTER,
  GRID_POSITIONS.TOP_RIGHT,
  GRID_POSITIONS.MIDDLE_LEFT,
  GRID_POSITIONS.CENTER,
  GRID_POSITIONS.MIDDLE_RIGHT,
  GRID_POSITIONS.BOTTOM_LEFT,
  GRID_POSITIONS.BOTTOM_CENTER,
  GRID_POSITIONS.BOTTOM_RIGHT,
];
