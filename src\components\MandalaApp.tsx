import React, { useState } from 'react';
import { MandalaGrid } from './MandalaGrid';
import { Navigation } from './Navigation';
import { HelpDialog } from './HelpDialog';
import { Settings } from './Settings';
import { LoadingSpinner } from './LoadingSpinner';
import { useMandala } from '../contexts/MandalaContext';
import { useAIGeneration } from '../hooks/useAIGeneration';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';
import { usePersistence } from '../hooks/usePersistence';
import { useAPISettings } from '../hooks/useAPISettings';
import { findNodeById } from '../utils/mandala';

export const MandalaApp: React.FC = () => {
  const { state, dispatch } = useMandala();
  const { smartGenerate, isGenerating } = useAIGeneration();
  const { saveChart, exportData, importData, lastSaveTimeText } = usePersistence();
  useKeyboardShortcuts(); // 启用键盘快捷键
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showHelpDialog, setShowHelpDialog] = useState(false);
  const [newChartTitle, setNewChartTitle] = useState('');
  
  // 处理节点导航
  const handleNavigate = (nodeId: string) => {
    const targetNode = findNodeById(state.currentChart?.rootNode!, nodeId);
    if (targetNode && targetNode.content.trim() !== '') {
      dispatch({ type: 'NAVIGATE_TO_NODE', payload: nodeId });
    }
  };
  
  // 处理AI生成
  const handleAIGenerate = async (nodeId: string) => {
    if (isGenerating) return;

    try {
      const success = await smartGenerate(nodeId);
      if (!success) {
        dispatch({ type: 'SET_ERROR', payload: 'AI生成失败，请稍后重试' });
      }
    } catch (error) {
      console.error('AI生成错误:', error);
      dispatch({ type: 'SET_ERROR', payload: 'AI生成过程中发生错误' });
    }
  };
  
  // 创建新图表
  const handleCreateNew = () => {
    setShowCreateDialog(true);
  };
  
  const handleConfirmCreate = () => {
    if (newChartTitle.trim()) {
      dispatch({ type: 'CREATE_CHART', payload: newChartTitle.trim() });
      setNewChartTitle('');
      setShowCreateDialog(false);
    }
  };
  
  const handleCancelCreate = () => {
    setNewChartTitle('');
    setShowCreateDialog(false);
  };
  
  // 保存功能
  const handleSave = () => {
    if (state.currentChart) {
      const success = saveChart(state.currentChart);
      if (success) {
        dispatch({ type: 'SET_ERROR', payload: null });
      } else {
        dispatch({ type: 'SET_ERROR', payload: '保存失败' });
      }
    }
  };
  
  // 导入功能
  const handleLoad = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const jsonData = e.target?.result as string;
            const success = importData(jsonData);
            if (success) {
              dispatch({ type: 'SET_ERROR', payload: null });
            } else {
              dispatch({ type: 'SET_ERROR', payload: '导入失败' });
            }
          } catch (error) {
            dispatch({ type: 'SET_ERROR', payload: '文件格式错误' });
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };
  
  // 导出功能
  const handleExport = () => {
    try {
      const dataStr = exportData();
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `mandala-export-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: '导出失败' });
    }
  };

  // 显示帮助
  const handleShowHelp = () => {
    setShowHelpDialog(true);
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* 导航栏 */}
      <Navigation
        onCreateNew={handleCreateNew}
        onSave={handleSave}
        onLoad={handleLoad}
        onExport={handleExport}
        onShowHelp={handleShowHelp}
        lastSaveTimeText={lastSaveTimeText}
      />
      
      {/* 主内容区域 */}
      <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        {state.currentChart ? (
          <div className="max-w-5xl mx-auto">
            {/* 当前视图标题 */}
            <div className="text-center mb-4 sm:mb-6 animate-fade-in">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gradient mb-2">
                {state.currentChart.title}
              </h1>
              <p className="text-sm sm:text-base text-gray-600 px-2">
                {state.isEditMode ? '编辑模式 - 点击格子编辑内容' : '浏览模式 - 点击格子进入下一层'}
              </p>
            </div>

            {/* 九宫格 */}
            <div className="card p-4 sm:p-6 animate-scale-in">
              {state.isLoading && (
                <div className="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center z-10">
                  <LoadingSpinner size="lg" text="AI正在生成内容..." />
                </div>
              )}
              <MandalaGrid
                onNavigate={handleNavigate}
                onAIGenerate={handleAIGenerate}
              />
            </div>
          </div>
        ) : (
          /* 欢迎页面 */
          <div className="max-w-2xl mx-auto text-center animate-fade-in">
            <div className="card p-8">
              <h1 className="text-3xl font-bold text-gradient mb-4">
                曼陀罗思维导图
              </h1>
              <p className="text-gray-600 mb-8 leading-relaxed">
                基于曼陀罗思维的九宫格思维导图工具，支持AI智能生成内容，
                帮助您系统化地思考和规划任何主题
              </p>
              <button
                onClick={handleCreateNew}
                className="btn-primary text-lg px-8 py-3"
              >
                创建新的曼陀罗图
              </button>
            </div>
          </div>
        )}
      </div>
      
      {/* 创建对话框 */}
      {showCreateDialog && (
        <div className="dialog-overlay">
          <div className="dialog-content p-6">
            <h2 className="text-xl font-bold mb-4 text-gradient">创建新的曼陀罗图</h2>
            <input
              type="text"
              value={newChartTitle}
              onChange={(e) => setNewChartTitle(e.target.value)}
              placeholder="请输入主题..."
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleConfirmCreate();
                } else if (e.key === 'Escape') {
                  handleCancelCreate();
                }
              }}
            />
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={handleCancelCreate}
                className="btn-ghost"
              >
                取消
              </button>
              <button
                onClick={handleConfirmCreate}
                disabled={!newChartTitle.trim()}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                创建
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 帮助对话框 */}
      <HelpDialog
        isOpen={showHelpDialog}
        onClose={() => setShowHelpDialog(false)}
      />
    </div>
  );
};
