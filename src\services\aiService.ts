import { AIGenerationRequest, AIGenerationResponse, MandalaNode, APISettings, AIProvider, GeminiModel, APIKeyValidationResult } from '../types/mandala';
import { getLevelName } from '../utils/mandala';
import { geminiProvider } from './aiProviders/geminiProvider';
import { storageService } from './storageService';



// 多提供商 AI 服务
class AIService {
  private currentSettings: APISettings | null = null;

  constructor() {
    // 从存储中加载设置
    this.loadSettings();
  }

  // 加载设置
  private loadSettings() {
    this.currentSettings = storageService.getAPISettings();
    this.updateProviderSettings();
  }

  // 更新提供商设置
  private updateProviderSettings() {
    if (!this.currentSettings) return;

    if (this.currentSettings.provider === 'gemini' && this.currentSettings.gemini?.apiKey) {
      geminiProvider.setApiKey(this.currentSettings.gemini.apiKey);
    }
  }

  // 设置 API 配置
  setAPISettings(settings: APISettings) {
    this.currentSettings = settings;
    storageService.saveAPISettings(settings);
    this.updateProviderSettings();
  }

  // 获取当前设置
  getCurrentSettings(): APISettings | null {
    return this.currentSettings;
  }

  // 验证 API 密钥
  async validateAPIKey(provider: AIProvider, apiKey: string): Promise<APIKeyValidationResult> {
    switch (provider) {
      case 'gemini':
        const tempProvider = new (await import('./aiProviders/geminiProvider')).GeminiProvider(apiKey);
        return await tempProvider.validateApiKey();
      case 'openai':
        // TODO: 实现 OpenAI 验证
        return { isValid: false, error: 'OpenAI 支持尚未实现' };
      default:
        return { isValid: false, error: '不支持的提供商' };
    }
  }

  // 获取可用模型
  async getAvailableModels(provider: AIProvider): Promise<GeminiModel[]> {
    if (!this.currentSettings) return [];

    switch (provider) {
      case 'gemini':
        if (this.currentSettings.gemini?.apiKey) {
          try {
            const models = await geminiProvider.getAvailableModels();
            // 更新存储的模型列表
            storageService.updateGeminiModels(models);
            return models;
          } catch (error) {
            console.error('获取 Gemini 模型失败:', error);
            // 返回缓存的模型列表
            return this.currentSettings.gemini.availableModels || [];
          }
        }
        return [];
      case 'openai':
        // TODO: 实现 OpenAI 模型获取
        return [];
      default:
        return [];
    }
  }

  // 设置API密钥（保持向后兼容）
  setApiKey(apiKey: string) {
    if (!this.currentSettings) {
      this.currentSettings = storageService.getAPISettings();
    }

    if (this.currentSettings.provider === 'gemini' && this.currentSettings.gemini) {
      this.currentSettings.gemini.apiKey = apiKey;
      this.setAPISettings(this.currentSettings);
    }
  }
  
  // 生成单个内容
  async generateSingleContent(request: AIGenerationRequest): Promise<string> {
    if (!this.currentSettings) {
      return this.getMockContent(request);
    }

    try {
      switch (this.currentSettings.provider) {
        case 'gemini':
          if (!this.currentSettings.gemini?.apiKey) {
            return this.getMockContent(request);
          }
          return await geminiProvider.generateContent(
            request,
            this.currentSettings.gemini.model,
            {
              maxTokens: this.currentSettings.maxTokens,
              temperature: this.currentSettings.temperature,
            }
          );
        case 'openai':
          // TODO: 实现 OpenAI 调用
          return this.getMockContent(request);
        default:
          return this.getMockContent(request);
      }
    } catch (error) {
      console.error('AI生成失败:', error);
      return this.getMockContent(request);
    }
  }
  
  // 批量生成内容（生成8个子项）
  async generateBatchContent(request: AIGenerationRequest): Promise<string[]> {
    if (!this.currentSettings) {
      return this.getMockBatchContent(request);
    }

    try {
      switch (this.currentSettings.provider) {
        case 'gemini':
          if (!this.currentSettings.gemini?.apiKey) {
            return this.getMockBatchContent(request);
          }
          return await geminiProvider.generateBatchContent(
            request,
            this.currentSettings.gemini.model,
            {
              maxTokens: this.currentSettings.maxTokens * 3, // 批量生成需要更多 tokens
              temperature: this.currentSettings.temperature,
            }
          );
        case 'openai':
          // TODO: 实现 OpenAI 批量调用
          return this.getMockBatchContent(request);
        default:
          return this.getMockBatchContent(request);
      }
    } catch (error) {
      console.error('AI批量生成失败:', error);
      return this.getMockBatchContent(request);
    }
  }
  

  
  // 获取模拟单个内容
  private getMockContent(request: AIGenerationRequest): string {
    const levelName = getLevelName(request.level);
    const mockContents = {
      2: ['目标设定', '资源配置', '时间规划', '风险管理', '团队协作', '技能提升', '成果评估', '持续改进'],
      3: ['具体目标', '实施计划', '资源需求', '时间节点', '责任分工', '质量标准', '监控机制', '调整策略'],
      4: ['制定计划', '收集信息', '分析现状', '设定目标', '制定策略', '执行行动', '监控进度', '总结反思'],
    };
    
    const contents = mockContents[request.level as keyof typeof mockContents] || [`${levelName}内容`];
    return contents[request.position % contents.length];
  }
  
  // 获取模拟批量内容
  private getMockBatchContent(request: AIGenerationRequest): string[] {
    const levelName = getLevelName(request.level);
    const mockBatches = {
      2: ['目标设定', '资源配置', '时间规划', '风险管理', '团队协作', '技能提升', '成果评估', '持续改进'],
      3: ['具体目标', '实施计划', '资源需求', '时间节点', '责任分工', '质量标准', '监控机制', '调整策略'],
      4: ['制定详细计划', '收集相关信息', '分析当前现状', '设定明确目标', '制定执行策略', '开始执行行动', '监控执行进度', '总结经验教训'],
    };
    
    return mockBatches[request.level as keyof typeof mockBatches] || 
           Array.from({ length: 8 }, (_, i) => `${levelName}${i + 1}`);
  }
}

// 导出单例
export const aiService = new AIService();
