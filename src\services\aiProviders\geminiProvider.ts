import { GeminiModel, APIKeyValidationResult, AIGenerationRequest } from '../../types/mandala';

// Gemini API 基础配置
const GEMINI_API_BASE_URL = 'https://generativelanguage.googleapis.com/v1beta';

// Gemini API 错误类型
interface GeminiError {
  error: {
    code: number;
    message: string;
    status: string;
  };
}

// Gemini API 响应类型
interface GeminiGenerateResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
    index: number;
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  }>;
  promptFeedback?: {
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  };
}

// Gemini 模型列表响应
interface GeminiModelsResponse {
  models: Array<{
    name: string;
    displayName: string;
    description: string;
    inputTokenLimit: number;
    outputTokenLimit: number;
    supportedGenerationMethods: string[];
    temperature: number;
    topP: number;
    topK: number;
  }>;
}

// Gemini API 提供商类
export class GeminiProvider {
  private apiKey: string = '';
  private baseURL: string = GEMINI_API_BASE_URL;

  constructor(apiKey?: string) {
    if (apiKey) {
      this.apiKey = apiKey;
    }
  }

  // 设置 API 密钥
  setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  // 验证 API 密钥并获取可用模型
  async validateApiKey(): Promise<APIKeyValidationResult> {
    if (!this.apiKey) {
      return {
        isValid: false,
        error: 'API 密钥不能为空',
      };
    }

    try {
      const models = await this.getAvailableModels();
      return {
        isValid: true,
        models,
      };
    } catch (error) {
      console.error('Gemini API 密钥验证失败:', error);
      return {
        isValid: false,
        error: error instanceof Error ? error.message : '密钥验证失败',
      };
    }
  }

  // 获取可用模型列表
  async getAvailableModels(): Promise<GeminiModel[]> {
    if (!this.apiKey) {
      throw new Error('API 密钥未设置');
    }

    try {
      const response = await fetch(`${this.baseURL}/models?key=${this.apiKey}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData: GeminiError = await response.json();
        throw new Error(`获取模型列表失败: ${errorData.error.message}`);
      }

      const data: GeminiModelsResponse = await response.json();
      
      // 过滤出支持文本生成的模型
      const textModels = data.models.filter(model => 
        model.supportedGenerationMethods.includes('generateContent')
      );

      return textModels.map(model => ({
        name: model.name,
        displayName: model.displayName,
        description: model.description,
        inputTokenLimit: model.inputTokenLimit,
        outputTokenLimit: model.outputTokenLimit,
        supportedGenerationMethods: model.supportedGenerationMethods,
        temperature: model.temperature,
        topP: model.topP,
        topK: model.topK,
      }));
    } catch (error) {
      console.error('获取 Gemini 模型列表失败:', error);
      throw error;
    }
  }

  // 生成单个内容
  async generateContent(
    request: AIGenerationRequest,
    model: string = 'gemini-1.5-flash',
    options: {
      maxTokens?: number;
      temperature?: number;
    } = {}
  ): Promise<string> {
    if (!this.apiKey) {
      throw new Error('API 密钥未设置');
    }

    const prompt = this.buildPrompt(request);
    const { maxTokens = 150, temperature = 0.7 } = options;

    try {
      const response = await fetch(
        `${this.baseURL}/models/${model}:generateContent?key=${this.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    text: prompt,
                  },
                ],
              },
            ],
            generationConfig: {
              temperature,
              maxOutputTokens: maxTokens,
              topP: 0.8,
              topK: 10,
            },
            safetySettings: [
              {
                category: 'HARM_CATEGORY_HARASSMENT',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE',
              },
              {
                category: 'HARM_CATEGORY_HATE_SPEECH',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE',
              },
              {
                category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE',
              },
              {
                category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE',
              },
            ],
          }),
        }
      );

      if (!response.ok) {
        const errorData: GeminiError = await response.json();
        throw new Error(`Gemini API 请求失败: ${errorData.error.message}`);
      }

      const data: GeminiGenerateResponse = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('Gemini API 未返回有效内容');
      }

      const content = data.candidates[0]?.content?.parts?.[0]?.text;
      if (!content) {
        throw new Error('Gemini API 返回内容为空');
      }

      return content.trim();
    } catch (error) {
      console.error('Gemini 内容生成失败:', error);
      throw error;
    }
  }

  // 批量生成内容
  async generateBatchContent(
    request: AIGenerationRequest,
    model: string = 'gemini-1.5-flash',
    options: {
      maxTokens?: number;
      temperature?: number;
    } = {}
  ): Promise<string[]> {
    const prompt = this.buildBatchPrompt(request);
    const content = await this.generateContent(
      { ...request, parentContent: prompt },
      model,
      { ...options, maxTokens: options.maxTokens || 500 }
    );

    // 解析返回的内容，按行分割
    const items = content
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .slice(0, 8); // 确保只取8个

    // 如果不足8个，用默认内容补充
    while (items.length < 8) {
      items.push(`内容${items.length + 1}`);
    }

    return items;
  }

  // 构建单个内容生成的提示词
  private buildPrompt(request: AIGenerationRequest): string {
    const { parentContent, level, context } = request;
    
    const levelNames = {
      1: '主题',
      2: '维度',
      3: '子主题',
      4: '执行步骤',
    };

    const levelName = levelNames[level as keyof typeof levelNames] || '内容';
    const contextStr = context.length > 0 ? context.join(' -> ') : '无';

    return `你是一个专业的思维导图专家。请基于给定的上下文，生成一个合适的${levelName}内容。

父级内容：${parentContent}
当前层级：${levelName}
上下文：${contextStr}

要求：
1. 生成一个与上下文相关的${levelName}内容
2. 内容要简洁明确，符合该层级的特点
3. 与其他内容形成良好的逻辑关联
4. 具有实用性和可操作性
5. 内容长度控制在20个字以内

请直接返回生成的内容，不需要额外说明：`;
  }

  // 构建批量内容生成的提示词
  private buildBatchPrompt(request: AIGenerationRequest): string {
    const { parentContent, level, context } = request;
    
    const levelNames = {
      2: '维度',
      3: '子主题',
      4: '执行步骤',
    };

    const levelName = levelNames[level as keyof typeof levelNames] || '内容';
    const contextStr = context.length > 0 ? context.join(' -> ') : '无';

    if (level === 4) {
      return `你是一个专业的思维导图专家。请基于给定的子主题，生成8个具体可执行的行动步骤。

主题：${context[0] || ''}
维度：${context[1] || ''}
子主题：${parentContent}
上下文：${contextStr}

要求：
1. 生成8个具体可执行的行动步骤
2. 每个步骤要明确、具体、可衡量
3. 步骤之间要有逻辑顺序
4. 每个步骤用动词开头，表达清晰（不超过20个字）
5. 确保步骤的可操作性和实用性

请直接返回8个执行步骤，每行一个，不需要编号：`;
    }

    return `你是一个专业的思维导图专家。请基于给定的主题，生成8个相关的${levelName}。

主题：${parentContent}
当前层级：${levelName}
上下文：${contextStr}

要求：
1. 生成8个与主题相关的${levelName}
2. 每个${levelName}要简洁明确，符合该层级的特点
3. ${levelName}之间要有逻辑关联，覆盖主题的不同方面
4. 具有实用性和可操作性
5. 每个${levelName}长度控制在15个字以内

请直接返回8个${levelName}，每行一个，不需要编号：`;
  }
}

// 导出默认实例
export const geminiProvider = new GeminiProvider();
